import { createServer, type Server } from 'node:http'
import { promisify } from 'node:util'
import { ParseUrlError } from '../app/errors/parse-url-error'
import { TimeoutError } from '../app/errors/timeout-error'
import { HttpMethod } from '../app/utils/sender/constants'
import { SenderRequestError } from '../app/utils/sender/errors/sender-request-error'
import { Sender } from '../app/utils/sender/sender'

// Mock Server Implementation
class MockServer {
    private readonly server: Server
    private port = 0
    private readonly responses = new Map<string, any>()
    private requestLog: any[] = []
    private readonly delays = new Map<string, number>()
    private readonly errors = new Map<string, Error>()

    constructor() {
        this.server = createServer((req, res) => {
            const url = req.url || '/'
            const method = req.method || 'GET'
            const key = `${method}:${url}`

            let body = ''
            req.on('data', (chunk) => { body += chunk })

            req.on('end', () => {
                this.requestLog.push({
                    method,
                    url,
                    headers: req.headers,
                    body,
                    timestamp: Date.now(),
                })

                const delay = this.delays.get(key) || 0
                const error = this.errors.get(key)
                const response = this.responses.get(key)

                setTimeout(() => {
                    if (error) {
                        res.destroy(error)

                        return
                    }

                    if (response) {
                        res.writeHead(response.status || 200, response.headers || {})
                        res.end(response.body || 'OK')
                    } else {
                        res.writeHead(200, { 'Content-Type': 'text/plain' })
                        res.end('Default Response')
                    }
                }, delay)
            })
        })
    }

    async start(): Promise<string> {
        await promisify(this.server.listen.bind(this.server))(0)
        const address = this.server.address()
        this.port = typeof address === 'string' ? 0 : address?.port || 0

        return `http://localhost:${this.port}`
    }

    async stop(): Promise<void> {
        await promisify(this.server.close.bind(this.server))()
    }

    setResponse(method: string, path: string, response: any): void {
        this.responses.set(`${method}:${path}`, response)
    }

    setDelay(method: string, path: string, delay: number): void {
        this.delays.set(`${method}:${path}`, delay)
    }

    setError(method: string, path: string, error: Error): void {
        this.errors.set(`${method}:${path}`, error)
    }

    getRequestLog(): any[] {
        return [...this.requestLog]
    }

    clearRequestLog(): void {
        this.requestLog = []
    }

    reset(): void {
        this.responses.clear()
        this.delays.clear()
        this.errors.clear()
        this.requestLog = []
    }
}

// Test Result Tracking
class TestResult {
    private passed = 0
    private failed = 0
    private total = 0

    assert(condition: boolean, message: string): void {
        this.total++

        if (condition) {
            this.passed++
            console.log(`✅ ${message}`)
        } else {
            this.failed++
            console.log(`❌ ${message}`)
        }
    }

    async assertAsync(fn: () => Promise<boolean>, message: string): Promise<void> {
        try {
            const result = await fn()
            this.assert(result, message)
        } catch (error) {
            this.assert(false, `${message} - Error: ${error}`)
        }
    }

    async assertThrows(fn: () => Promise<any>, expectedError: any, message: string): Promise<void> {
        this.total++

        try {
            await fn()
            this.failed++
            console.log(`❌ ${message} - Expected error but got success`)
        } catch (error) {
            if (expectedError && !(error instanceof expectedError)) {
                this.failed++
                console.log(`❌ ${message} - Expected ${expectedError.name} but got ${error.constructor.name}`)
            } else {
                this.passed++
                console.log(`✅ ${message}`)
            }
        }
    }

    summary(): void {
        console.log(`\n📊 Test Summary:`)
        console.log(`Total: ${this.total}`)
        console.log(`Passed: ${this.passed}`)
        console.log(`Failed: ${this.failed}`)
        console.log(`Success Rate: ${this.total > 0 ? ((this.passed / this.total) * 100).toFixed(2) : 0}%`)

        if (this.failed > 0) {
            console.log(`\n🚨 ${this.failed} test(s) failed!`)
        } else {
            console.log(`\n🎉 All tests passed!`)
        }
    }
}

// Test Runner
async function runTests(): Promise<void> {
    const mockServer = new MockServer()
    const test = new TestResult()
    let baseUrl = ''

    try {
        // Start mock server
        baseUrl = await mockServer.start()
        console.log(`🚀 Mock server started at ${baseUrl}`)

        // Constructor Tests
        console.log('\n🧪 Constructor Tests')

        // Valid URL parsing
        const sender1 = new Sender(`${baseUrl}/api/test`)
        test.assert(sender1.origin === baseUrl, 'Should parse origin correctly')
        test.assert(sender1.path === '/api/test', 'Should parse path correctly')

        // Invalid URL handling
        await test.assertThrows(
            async () => new Sender('invalid-url'),
            ParseUrlError,
            'Should throw ParseUrlError for invalid URL',
        )

        // Custom options
        const sender2 = new Sender(baseUrl, {
            method: HttpMethod.GET,
            timeout: 5000,
            headers: { 'X-Test': 'value' },
            retry: { retries: 5 },
        })

        test.assert(sender2.activeRequests === 0, 'Should initialize activeRequests to 0')

        // DNS cache disabled
        const sender3 = new Sender(baseUrl, { dns: false })
        test.assert(sender3.connections >= 0, 'Should initialize with DNS cache disabled')

        // Custom response body formatter
        const sender4 = new Sender<{ parsed: boolean }>(baseUrl, {
            responseBodyFormatter: (body) => ({ parsed: true, original: body }),
        })

        // Basic Request Tests
        console.log('\n🧪 Basic Request Tests')

        // Successful GET request
        mockServer.setResponse('GET', '/test', { status: 200, body: 'Success' })
        const response1 = await sender1.send(null, { method: HttpMethod.GET })
        test.assert(response1.status === 200, 'Should handle successful GET request')
        test.assert(response1.body === 'Success', 'Should return correct response body')
        test.assert(typeof response1.id === 'string', 'Should generate request ID')
        test.assert(typeof response1.took === 'bigint', 'Should measure request time')

        // POST request with body
        mockServer.setResponse('POST', '/test', { status: 201, body: 'Created' })
        const response2 = await sender1.send('{"data": "test"}')
        test.assert(response2.status === 201, 'Should handle POST request with body')

        // Custom headers
        mockServer.setResponse('GET', '/headers', { status: 200, body: 'OK' })

        await sender1.send(null, {
            method: HttpMethod.GET,
            headers: { 'X-Custom': 'header' },
        })

        const lastRequest = mockServer.getRequestLog().pop()
        test.assert(lastRequest.headers['x-custom'] === 'header', 'Should send custom headers')

        // Metadata handling
        const response3 = await sender1.send(null, {
            method: HttpMethod.GET,
            metadata: { userId: 123 },
        })

        test.assert(response3.metadata.userId === 123, 'Should preserve request metadata')

        // Error Handling Tests
        console.log('\n🧪 Error Handling Tests')

        // HTTP error status
        mockServer.setResponse('GET', '/error', { status: 404, body: 'Not Found' })
        const response4 = await sender1.send(null, { method: HttpMethod.GET })
        test.assert(response4.status === 404, 'Should handle HTTP error status codes')

        // Network error
        mockServer.setError('GET', '/network-error', new Error('Connection refused'))

        await test.assertThrows(
            async () => await sender1.send(null, { method: HttpMethod.GET }),
            SenderRequestError,
            'Should throw SenderRequestError for network errors',
        )

        // Timeout Tests
        console.log('\n🧪 Timeout Tests')

        const shortTimeoutSender = new Sender(baseUrl, { timeout: 100 })
        mockServer.setDelay('POST', '/slow', 200)
        mockServer.setResponse('POST', '/slow', { status: 200, body: 'Slow response' })

        await test.assertThrows(
            async () => await shortTimeoutSender.send('data'),
            TimeoutError,
            'Should timeout on slow requests',
        )

        // Custom timeout per request
        await test.assertThrows(
            async () => await sender1.send('data', { timeout: 50 }),
            TimeoutError,
            'Should respect per-request timeout',
        )

        // AbortSignal Tests
        console.log('\n🧪 AbortSignal Tests')

        const abortController = new AbortController()
        mockServer.setDelay('POST', '/abort-test', 100)

        const abortPromise = sender1.send('data', { signal: abortController.signal })
        setTimeout(() => abortController.abort(), 50)

        await test.assertThrows(
            async () => await abortPromise,
            SenderRequestError,
            'Should handle aborted requests',
        )

        // Event Emitting Tests
        console.log('\n🧪 Event Emitting Tests')

        const activeRequestsEvents: number[] = []
        const requestEvents: any[] = []
        const responseEvents: any[] = []
        const errorEvents: any[] = []

        const eventSender = new Sender(baseUrl)
        eventSender.on('activeRequests', (count) => activeRequestsEvents.push(count))
        eventSender.on('request', (req) => requestEvents.push(req))
        eventSender.on('response', (res) => responseEvents.push(res))
        eventSender.on('error', (err) => errorEvents.push(err))

        mockServer.setResponse('POST', '/events', { status: 200, body: 'Event test' })
        await eventSender.send('test')

        test.assert(activeRequestsEvents.length >= 2, 'Should emit activeRequests events')
        test.assert(requestEvents.length === 1, 'Should emit request events')
        test.assert(responseEvents.length === 1, 'Should emit response events')

        // Error event
        mockServer.setError('POST', '/error-event', new Error('Test error'))

        try {
            await eventSender.send('error-test')
        } catch {}

        test.assert(errorEvents.length === 1, 'Should emit error events')

        // Retry Logic Tests
        console.log('\n🧪 Retry Logic Tests')

        let retryAttempts = 0

        const retrySender = new Sender(`${baseUrl}/retry`, {
            retry: {
                retries: 3,
                delay: 10,
                onFailedAttempt: async () => { retryAttempts++ },
            },
        })

        // Setup failing then succeeding response
        let callCount = 0

        mockServer.setResponse('POST', '/retry', {
            status: () => (++callCount > 2 ? 200 : 500),
            body: 'Retry success',
        })

        const retryResponse = await retrySender.send('retry-test')
        test.assert(retryResponse.status === 200, 'Should eventually succeed with retry')
        test.assert(retryAttempts >= 2, 'Should attempt retries on failure')

        // Retry disabled
        const noRetrySender = new Sender(baseUrl, { retry: false })
        mockServer.setResponse('POST', '/no-retry', { status: 500, body: 'Server Error' })
        const noRetryResponse = await noRetrySender.send('no-retry')
        test.assert(noRetryResponse.status === 500, 'Should not retry when disabled')

        // shouldRetryOnResponse callback
        const conditionalRetrySender = new Sender(baseUrl, {
            retry: {
                retries: 2,
                delay: 10,
                shouldRetryOnResponse: async (response) => response.status >= 500,
            },
        })

        mockServer.setResponse('POST', '/conditional', { status: 400, body: 'Client Error' })
        const conditionalResponse = await conditionalRetrySender.send('conditional')
        test.assert(conditionalResponse.status === 400, 'Should not retry on client errors')

        // Response Body Formatter Tests
        console.log('\n🧪 Response Body Formatter Tests')

        const jsonSender = new Sender<any>(baseUrl, {
            responseBodyFormatter: (body) => JSON.parse(body),
        })

        mockServer.setResponse('POST', '/json', {
            status: 200,
            body: '{"message": "JSON response"}',
            headers: { 'Content-Type': 'application/json' },
        })

        const jsonResponse = await jsonSender.send('json-test')
        test.assert(jsonResponse.body.message === 'JSON response', 'Should format response body as JSON')

        // Formatter with AbortSignal
        const abortFormatterSender = new Sender<string>(baseUrl, {
            responseBodyFormatter: async (body, signal) => {
                signal?.throwIfAborted()
                await new Promise((resolve) => setTimeout(resolve, 10))

                return body.toUpperCase()
            },
        })

        mockServer.setResponse('POST', '/formatter', { status: 200, body: 'lowercase' })
        const formattedResponse = await abortFormatterSender.send('formatter-test')
        test.assert(formattedResponse.body === 'LOWERCASE', 'Should apply custom formatter')

        // Connection Pooling Tests
        console.log('\n🧪 Connection Pooling Tests')

        const poolSender = new Sender(baseUrl)
        const initialConnections = poolSender.connections

        // Multiple concurrent requests
        mockServer.setResponse('POST', '/concurrent', { status: 200, body: 'Concurrent' })

        const concurrentPromises = Array.from({ length: 5 }, () => poolSender.send('concurrent-test'))

        const concurrentResults = await Promise.all(concurrentPromises)
        test.assert(concurrentResults.every((r) => r.status === 200), 'Should handle concurrent requests')
        test.assert(poolSender.connections >= initialConnections, 'Should manage connection pool')

        // Edge Cases Tests
        console.log('\n🧪 Edge Cases Tests')

        // Empty body
        mockServer.setResponse('POST', '/empty', { status: 200, body: '' })
        const emptyResponse = await sender1.send('')
        test.assert(emptyResponse.body === '', 'Should handle empty response body')

        // Null body
        const nullResponse = await sender1.send(null)
        test.assert(nullResponse.status === 200, 'Should handle null request body')

        // Large body
        const largeBody = 'x'.repeat(10_000)
        mockServer.setResponse('POST', '/large', { status: 200, body: 'Large handled' })
        const largeResponse = await sender1.send(largeBody)
        test.assert(largeResponse.status === 200, 'Should handle large request bodies')

        // Different HTTP methods
        const methods = [HttpMethod.GET, HttpMethod.PUT, HttpMethod.DELETE, HttpMethod.PATCH]

        for (const method of methods) {
            mockServer.setResponse(method, '/method-test', { status: 200, body: `${method} OK` })
            const methodResponse = await sender1.send('method-test', { method })
            test.assert(methodResponse.status === 200, `Should handle ${method} requests`)
        }

        // URL with query parameters and hash
        const complexSender = new Sender(`${baseUrl}/api/endpoint?param=value#section`)
        test.assert(complexSender.path === '/api/endpoint?param=value#section', 'Should handle complex URLs')

        // Memory Management Tests
        console.log('\n🧪 Memory Management Tests')

        // Multiple senders to same origin
        const sender5 = new Sender(baseUrl)
        const sender6 = new Sender(baseUrl)

        mockServer.setResponse('GET', '/memory', { status: 200, body: 'Memory test' })

        await Promise.all([
            sender5.send(null, { method: HttpMethod.GET }),
            sender6.send(null, { method: HttpMethod.GET }),
        ])

        test.assert(sender5.connections >= 0 && sender6.connections >= 0, 'Should manage multiple sender instances')

        // Custom Pool Options Tests
        console.log('\n🧪 Custom Pool Options Tests')

        const customPoolSender = new Sender(baseUrl, {
            pool: {
                connections: 10,
                keepAliveTimeoutThreshold: 1000,
            },
        })

        mockServer.setResponse('POST', '/pool', { status: 200, body: 'Pool test' })
        const poolResponse = await customPoolSender.send('pool-test')
        test.assert(poolResponse.status === 200, 'Should work with custom pool options')

        // DNS Cache Tests
        console.log('\n🧪 DNS Cache Tests')

        const dnsCacheSender = new Sender(baseUrl, {
            dns: {
                enabled: true,
                maxItems: 100,
                maxTTL: 30_000,
            },
        })

        mockServer.setResponse('POST', '/dns', { status: 200, body: 'DNS test' })
        const dnsResponse = await dnsCacheSender.send('dns-test')
        test.assert(dnsResponse.status === 200, 'Should work with DNS cache enabled')

        // Response Headers Tests
        console.log('\n🧪 Response Headers Tests')

        mockServer.setResponse('GET', '/headers-test', {
            status: 200,
            body: 'Headers test',
            headers: {
                'Content-Type': 'text/plain',
                'X-Custom-Header': 'custom-value',
                'Set-Cookie': ['cookie1=value1', 'cookie2=value2'],
            },
        })

        const headerResponse = await sender1.send(null, { method: HttpMethod.GET })

        test.assert(
            headerResponse.headers['content-type'] === 'text/plain',
            'Should handle response headers correctly',
        )

        console.log('\n🏁 All tests completed!')
    } catch (error) {
        console.error('❌ Test execution failed:', error)
    } finally {
        await mockServer.stop()
        test.summary()
    }
}

// Run the tests
runTests().catch(console.error)

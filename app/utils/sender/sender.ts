import type { HttpMethodType, SenderRequest, SenderResponse } from './types'
import { isNullish, type Nullable } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tap } from '@kdt310722/utils/function'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { abortable, type Awaitable, pTap } from '@kdt310722/utils/promise'
import { type Dispatcher, interceptors, Pool } from 'undici'
import { TimeoutError } from '../../errors/timeout-error'
import { parseUrl } from '../urls'
import { HttpMethod } from './constants'
import { SenderRequestError } from './errors/sender-request-error'
import { type SenderRetryOptions, withRetry } from './retry'
import { formatHttpMethod, formatResponseHeader } from './utils'

export type DnsCacheOptions = interceptors.DNSInterceptorOpts & { enabled?: boolean }

export type ResponseBodyFormatter<TBody> = (body: string, signal?: AbortSignal) => Awaitable<TBody>

export interface SenderOptions<TBody> {
    pool?: Pool.Options
    dns?: DnsCacheOptions | boolean
    retry?: SenderRetryOptions<SenderResponse<TBody>> | boolean
    timeout?: number
    method?: HttpMethodType
    headers?: Record<string, string>
    responseBodyFormatter?: ResponseBodyFormatter<TBody>
}

export type SenderEvents<TBody> = {
    activeRequests: (count: number) => void
    connections: (count: number) => void
    request: (request: SenderRequest) => void
    response: (response: SenderResponse<TBody>) => void
    error: (error: SenderRequestError) => void
}

export interface SenderSendOptions extends Omit<SenderOptions<unknown>, 'pool' | 'dns' | 'responseBodyFormatter'> {
    signal?: AbortSignal
    metadata?: Record<string, unknown>
}

export class Sender<TBody = string> extends Emitter<SenderEvents<TBody>> {
    public readonly origin: string
    public readonly path: string

    public activeRequests = 0

    protected readonly pool: Pool
    protected readonly headers: Record<string, string>
    protected readonly retryOptions: SenderRetryOptions<SenderResponse<TBody>>
    protected readonly timeout: number
    protected readonly method: Dispatcher.HttpMethod
    protected readonly responseBodyFormatter: ResponseBodyFormatter<TBody>

    protected incrementId = 0n

    public constructor(url: string, { pool, headers = {}, retry = true, timeout = 10_000, method = HttpMethod.POST, dns = true, responseBodyFormatter = (body) => body as TBody }: SenderOptions<TBody> = {}) {
        super()

        const { origin, path } = parseUrl(url)

        this.origin = origin
        this.path = path
        this.pool = this.createPool(origin, pool, resolveNestedOptions(dns) || { enabled: false })
        this.headers = headers
        this.retryOptions = this.resolveRetryOptions(retry)
        this.timeout = timeout
        this.method = formatHttpMethod(method)
        this.responseBodyFormatter = responseBodyFormatter
    }

    public get connections() {
        return this.pool.stats.connected
    }

    public async send(body?: Nullable<string>, options: SenderSendOptions = {}) {
        this.emit('activeRequests', ++this.activeRequests)

        return this.processSend(body, options).finally(() => {
            this.emit('activeRequests', --this.activeRequests)
        })
    }

    protected async processSend(body?: Nullable<string>, options: SenderSendOptions = {}) {
        options.signal?.throwIfAborted()

        const start = process.hrtime.bigint()
        const abortController = new AbortController()
        const signal = AbortSignal.any([abortController.signal, ...(options.signal ? [options.signal] : [])])
        const timeoutId = setTimeout(() => abortController.abort(new TimeoutError()), options.timeout ?? this.timeout)

        const id = String(++this.incrementId)
        const method = options.method ? formatHttpMethod(options.method) : this.method
        const headers = { ...this.headers, ...options.headers }
        const metadata = options.metadata ?? {}
        const request: SenderRequest = { id, method, headers, body: body ?? undefined, metadata }

        this.emit('request', request)

        let response_: Dispatcher.ResponseData | undefined
        let result_: SenderResponse<TBody> | undefined

        try {
            const execute = async (signal?: AbortSignal) => {
                signal?.throwIfAborted()

                const response = response_ = await this.pool.request({ origin: this.origin, path: this.path, blocking: false, method, body, headers, signal })
                const responseBody = await this.responseBodyFormatter(await abortable(response.body.text(), signal), signal)
                const result: SenderResponse<TBody> = result_ = { id, status: response.statusCode, headers: formatResponseHeader(response.headers), body: responseBody, metadata, took: process.hrtime.bigint() - start }

                return result
            }

            return await withRetry(execute, { ...this.getRequestRetryOptions(options.retry), signal }).then(pTap((r) => this.emit('response', r)))
        } catch (error) {
            const error_ = tap(error instanceof SenderRequestError ? error : new SenderRequestError('Request failed', { cause: error }).withRequest(request).withUndiciResponse(response_).withResponse(result_), (err) => {
                this.emit('error', err)
            })

            throw error_
        } finally {
            clearTimeout(timeoutId)
        }
    }

    protected createPool(origin: string, options: Pool.Options = {}, { enabled: dnsCacheEnabled = true, ...dnsCacheOptions }: DnsCacheOptions = {}) {
        const pool = new Pool(origin, options)

        pool.on('connect', () => this.emit('connections', pool.stats.connected))
        pool.on('disconnect', () => this.emit(`connections`, pool.stats.connected))

        if (!dnsCacheEnabled) {
            return pool
        }

        return pool.compose([interceptors.dns(dnsCacheOptions)]) as Pool
    }

    protected getRequestRetryOptions(options?: SenderRetryOptions<SenderResponse<TBody>> | boolean) {
        if (isNullish(options)) {
            return this.retryOptions
        }

        return { ...this.retryOptions, ...this.resolveRetryOptions(options) }
    }

    protected resolveRetryOptions(options: SenderRetryOptions<SenderResponse<TBody>> | boolean) {
        return resolveNestedOptions(options) || { enabled: false }
    }
}
